"use server";

import { createClient } from "@/utils/supabase/server";

// Type for Supabase storage file objects
interface StorageFileObject {
  name: string;
  id?: string;
  updated_at?: string;
  created_at?: string;
  last_accessed_at?: string;
  metadata?: Record<string, any>;
  [key: string]: unknown;
}

export interface StorageImage {
  id: string;
  name: string;
  url: string;
  size: number;
  created_at: string;
  metadata?: {
    width?: number;
    height?: number;
  };
}

/**
 * Fetches previously uploaded images for the current business user
 */
export async function getBusinessImageLibrary(
  limit: number = 50,
  path: string = ""
): Promise<{ images: StorageImage[]; error?: string }> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { images: [], error: "User not authenticated." };
  }

  const bucketName = "business";
  const basePath = `${user.id}/products`;
  const fullPath = path ? `${basePath}/${path}` : basePath;

  console.log(`Fetching images from path: ${fullPath}`);

  try {
    // Array to store all collected images
    const images: StorageImage[] = [];

    // First, list all items in the user's products directory
    const { data: rootItems, error: rootListError } = await supabase.storage
      .from(bucketName)
      .list(fullPath, {
        limit: 100, // Increased limit to get more folders
        sortBy: { column: 'created_at', order: 'desc' }
      });

    if (rootListError) {
      console.error("Error listing product directory:", rootListError);
      return { images: [], error: `Failed to list product directory: ${rootListError.message}` };
    }

    if (!rootItems || rootItems.length === 0) {
      console.log("No items found in products directory");
      return { images: [] };
    }

    console.log(`Found ${rootItems.length} items in products directory`);

    // Process direct image files in the products directory
    const directImageFiles = rootItems.filter((item: StorageFileObject) => {
      if (!item || !item.name) return false;

      const isImageFile =
        item.name.endsWith('.webp') ||
        item.name.endsWith('.jpg') ||
        item.name.endsWith('.jpeg') ||
        item.name.endsWith('.png');

      return isImageFile;
    });

    // Process direct image files
    for (const file of directImageFiles) {
      try {
        const filePath = `${fullPath}/${file.name}`;
        console.log(`Getting public URL for direct file: ${filePath}`);

        const { data: urlData } = supabase.storage
          .from(bucketName)
          .getPublicUrl(filePath);

        if (urlData?.publicUrl) {
          images.push({
            id: file.id || `file-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            name: file.name,
            url: urlData.publicUrl,
            size: file.metadata?.size || 0,
            created_at: file.created_at || new Date().toISOString(),
            metadata: {
              width: file.metadata?.width,
              height: file.metadata?.height
            }
          });
        }
      } catch (error) {
        console.error(`Error getting public URL for direct file ${file.name}:`, error);
      }
    }

    // Find all product folders (they typically start with a UUID followed by underscore)
    const productFolders = rootItems.filter((item: StorageFileObject) => {
      if (!item || !item.name) return false;

      // Check if it's a folder (no file extension in name)
      const hasNoExtension = !item.name.includes('.') ||
                            (item.id && typeof item.id === 'string' && !item.id.includes('.'));

      // Product folders typically follow the pattern: {productId}_{product_name}
      const isProductFolder = hasNoExtension &&
                             (item.name.match(/^[a-f0-9-]{36}_/) || // UUID v4 format
                              item.name.match(/^[a-f0-9]{24}_/));   // ObjectId format

      return isProductFolder;
    });

    console.log(`Found ${productFolders.length} product folders`);

    // Process each product folder to find images
    for (const folder of productFolders) {
      if (!folder.name) continue;

      const folderPath = `${fullPath}/${folder.name}`;
      console.log(`Listing files in product folder: ${folderPath}`);

      try {
        const { data: folderFiles, error: folderError } = await supabase.storage
          .from(bucketName)
          .list(folderPath, { limit: 20 });

        if (folderError) {
          console.error(`Error listing files in folder ${folder.name}:`, folderError);
          continue;
        }

        if (!folderFiles || folderFiles.length === 0) {
          console.log(`No files found in folder: ${folder.name}`);
          continue;
        }

        console.log(`Found ${folderFiles.length} files in folder: ${folder.name}`);

        // Filter for image files in the folder
        const folderImageFiles = folderFiles.filter((file: FileObject) => {
          if (!file || !file.name) return false;

          return file.name.endsWith('.webp') ||
                file.name.endsWith('.jpg') ||
                file.name.endsWith('.jpeg') ||
                file.name.endsWith('.png');
        });

        console.log(`Found ${folderImageFiles.length} image files in folder: ${folder.name}`);

        // Get public URLs for all image files in the folder
        for (const imageFile of folderImageFiles) {
          try {
            const filePath = `${folderPath}/${imageFile.name}`;
            console.log(`Getting public URL for product image: ${filePath}`);

            const { data: urlData } = supabase.storage
              .from(bucketName)
              .getPublicUrl(filePath);

            if (urlData?.publicUrl) {
              // Extract product name from folder for better display
              const productName = folder.name.includes('_')
                ? folder.name.split('_').slice(1).join('_')
                : folder.name;

              images.push({
                id: imageFile.id || `folder-file-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                name: `${productName}/${imageFile.name}`,
                url: urlData.publicUrl,
                size: imageFile.metadata?.size || 0,
                created_at: imageFile.created_at || new Date().toISOString(),
                metadata: {
                  width: imageFile.metadata?.width,
                  height: imageFile.metadata?.height
                }
              });
            }
          } catch (error) {
            console.error(`Error getting public URL for image ${imageFile.name}:`, error);
          }
        }
      } catch (error) {
        console.error(`Error processing folder ${folder.name}:`, error);
      }
    }

    // Sort images by creation date (newest first)
    images.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

    console.log(`Total images found: ${images.length}`);

    // Return the images, limited to the requested amount
    return { images: images.slice(0, limit) };
  } catch (error) {
    console.error("Error fetching image library:", error);
    return {
      images: [],
      error: error instanceof Error ? error.message : "An unexpected error occurred"
    };
  }
}
