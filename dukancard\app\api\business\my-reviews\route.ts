import { createClient } from '@/utils/supabase/server';

import { NextRequest, NextResponse } from 'next/server';
import { TABLES, COLUMNS } from "@/lib/supabase/constants";
import { Database } from "@/types/supabase";

// Define interfaces for the expected data structure
interface BusinessProfileDataForReview {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
}

interface ReviewWithProfile {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: BusinessProfileDataForReview | null;
}



// Constants - optimized for 2-column grid (1x8, 2x4)
const REVIEWS_PER_PAGE = 8;

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Get user authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const sortBy = searchParams.get('sort') || 'newest';

    // For business-given reviews, we don't need to check the business profile
    // since we're filtering by user_id directly

    // Calculate pagination
    const from = (page - 1) * REVIEWS_PER_PAGE;
    const to = from + REVIEWS_PER_PAGE - 1;

    // Simple query without join for better performance
    let baseQuery = supabase
      .from(TABLES.RATINGS_REVIEWS)
      .select(`
        ${COLUMNS.ID},
        rating,
        review_text,
        ${COLUMNS.CREATED_AT},
        ${COLUMNS.UPDATED_AT},
        ${COLUMNS.BUSINESS_PROFILE_ID},
        ${COLUMNS.USER_ID}
      `, { count: 'exact' })
      .eq(COLUMNS.USER_ID, user.id);

    // Apply sorting to the base query
    switch (sortBy) {
      case "oldest":
        baseQuery = baseQuery.order(COLUMNS.CREATED_AT, { ascending: true });
        break;
      case "highest_rating":
        baseQuery = baseQuery.order("rating", { ascending: false });
        break;
      case "lowest_rating":
        baseQuery = baseQuery.order("rating", { ascending: true });
        break;
      case "newest":
      default:
        baseQuery = baseQuery.order(COLUMNS.CREATED_AT, { ascending: false });
        break;
    }

    // Get count first (without pagination)
    const { count: totalCount, error: countError } = await baseQuery;

    if (countError) {
      return NextResponse.json(
        { error: 'Failed to count reviews' },
        { status: 500 }
      );
    }

    // Now get the actual data with pagination
    const { data: reviews, error: reviewsError } = await baseQuery.range(from, to);

    if (reviewsError) {
      return NextResponse.json(
        { error: 'Failed to fetch reviews' },
        { status: 500 }
      );
    }

    // Process the reviews - fetch business profiles separately for better performance
    let typedReviews: ReviewWithProfile[] = [];

    if (reviews && reviews.length > 0) {
      // Fetch business profiles separately for the paginated reviews only
      const businessProfileIds = reviews.map((review: { business_profile_id: string }) => review.business_profile_id);
      const { data: businessProfiles } = await supabase
        .from(TABLES.BUSINESS_PROFILES)
        .select('id, business_name, business_slug, logo_url')
        .in('id', businessProfileIds);

      typedReviews = reviews.map((review: any) => {
        const businessProfile = businessProfiles?.find((profile: BusinessProfileDataForReview) => profile.id === review.business_profile_id) || null;
        return {
          ...review,
          business_profiles: businessProfile
        };
      });
    }

    // Calculate total pages
    const totalPages = Math.ceil((totalCount || 0) / REVIEWS_PER_PAGE);

    return NextResponse.json({
      reviews: typedReviews,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        perPage: REVIEWS_PER_PAGE
      }
    });

  } catch (_error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
