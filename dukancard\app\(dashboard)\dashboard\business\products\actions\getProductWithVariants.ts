"use server";

import { createClient } from "@/utils/supabase/server";
import { ProductServiceData } from "./schemas";
import { RpcProductVariant, ProductVariant } from "@/types/variants";

// Extended product type with variants
export interface ProductWithVariantsData extends ProductServiceData {
  variant_count: number;
  variants: RpcProductVariant[];
}

// Get a single product with all its variants (for business dashboard)
export async function getProductWithVariants(
  productId: string
): Promise<{ success: boolean; error?: string; data?: ProductWithVariantsData }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user)
    return { success: false, error: "User not authenticated." };

  try {
    // Use the database function to get product with variants
    const { data: result, error: functionError } = await supabase
      .rpc("get_product_with_variants", { product_uuid: productId });

    if (functionError) {
      console.error("Error calling get_product_with_variants:", functionError);
      return { success: false, error: "Failed to fetch product data." };
    }

    if (!result || result.length === 0) {
      return { success: false, error: "Product not found." };
    }

    const productData = result[0];

    // Verify that the product belongs to the authenticated user
    const { data: product, error: ownershipError } = await supabase
      .from("products_services")
      .select("business_id")
      .eq("id", productId)
      .eq("business_id", user.id)
      .single();

    if (ownershipError || !product) {
      return { success: false, error: "Product not found or access denied." };
    }

    // Transform the data to match our expected format
    const transformedData: ProductWithVariantsData = {
      id: productData.product_id,
      business_id: user.id,
      product_type: "physical", // Default since not returned by function
      name: productData.product_name,
      description: productData.product_description,
      base_price: productData.product_base_price,
      discounted_price: productData.product_discounted_price,
      is_available: productData.product_is_available,
      images: productData.product_images || [],
      featured_image_index: productData.product_featured_image_index || 0,
      slug: undefined, // Not returned by function
      created_at: (productData as any).product_created_at || new Date().toISOString(),
      updated_at: (productData as any).product_updated_at || new Date().toISOString(),
      variant_count: Number(productData.variant_count) || 0,
      variants: Array.isArray(productData.variants) ? (productData.variants as unknown as RpcProductVariant[]) : [],
    };

    return { success: true, data: transformedData };
  } catch (error) {
    console.error("Unexpected error in getProductWithVariants:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}

// Get available variants for a product (public-facing, for customer pages)
export async function getAvailableProductVariants(
  productId: string
): Promise<{ success: boolean; error?: string; data?: ProductVariant[] }> {
  const supabase = await createClient();

  try {
    // Use the database function to get available variants
    const { data: variants, error: functionError } = await supabase
      .rpc("get_available_product_variants", { product_uuid: productId });

    if (functionError) {
      console.error("Error calling get_available_product_variants:", functionError);
      return { success: false, error: "Failed to fetch variant data." };
    }

    return { success: true, data: (variants || []) as ProductVariant[] };
  } catch (error) {
    console.error("Unexpected error in getAvailableProductVariants:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}

// Get variants for a product with filtering and sorting (for business dashboard)
export async function getProductVariants(
  productId: string,
  options: {
    includeUnavailable?: boolean;
    sortBy?: "created_asc" | "created_desc" | "name_asc" | "name_desc" | "price_asc" | "price_desc";
    limit?: number;
    offset?: number;
  } = {}
): Promise<{ success: boolean; error?: string; data?: ProductVariant[]; count?: number }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user)
    return { success: false, error: "User not authenticated." };

  try {
    // Verify that the product belongs to the authenticated user
    const { data: product, error: ownershipError } = await supabase
      .from("products_services")
      .select("business_id")
      .eq("id", productId)
      .eq("business_id", user.id)
      .single();

    if (ownershipError || !product) {
      return { success: false, error: "Product not found or access denied." };
    }

    // Build the query
    let query = supabase
      .from("product_variants")
      .select("*", { count: "exact" })
      .eq("product_id", productId);

    // Apply filters
    if (!options.includeUnavailable) {
      query = query.eq("is_available", true);
    }

    // Apply sorting
    switch (options.sortBy) {
      case "created_asc":
        query = query.order("created_at", { ascending: true });
        break;
      case "created_desc":
        query = query.order("created_at", { ascending: false });
        break;
      case "name_asc":
        query = query.order("variant_name", { ascending: true });
        break;
      case "name_desc":
        query = query.order("variant_name", { ascending: false });
        break;
      case "price_asc":
        query = query.order("base_price", { ascending: true, nullsFirst: false });
        break;
      case "price_desc":
        query = query.order("base_price", { ascending: false, nullsFirst: true });
        break;
      default:
        query = query.order("created_at", { ascending: false });
    }

    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }
    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data: variants, error: queryError, count } = await query;

    if (queryError) {
      console.error("Error fetching variants:", queryError);
      return { success: false, error: "Failed to fetch variants." };
    }

    // Transform variant_values from JSON string to object
    const transformedVariants = variants?.map((variant) => ({
      ...variant,
      variant_values: typeof variant.variant_values === 'string' 
        ? JSON.parse(variant.variant_values) 
        : variant.variant_values,
    })) || [];

    return { 
      success: true, 
      data: transformedVariants,
      count: count || 0
    };
  } catch (error) {
    console.error("Unexpected error in getProductVariants:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}

// Get variant statistics for a business
export async function getBusinessVariantStats(): Promise<{ 
  success: boolean; 
  error?: string; 
  data?: {
    total_products: number;
    products_with_variants: number;
    total_variants: number;
    available_variants: number;
  }
}> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user)
    return { success: false, error: "User not authenticated." };

  try {
    // Use the database function to get variant statistics
    const { data: stats, error: functionError } = await supabase
      .rpc("get_business_variant_stats", { business_uuid: user.id });

    if (functionError) {
      console.error("Error calling get_business_variant_stats:", functionError);
      return { success: false, error: "Failed to fetch variant statistics." };
    }

    if (!stats || stats.length === 0) {
      return { 
        success: true, 
        data: {
          total_products: 0,
          products_with_variants: 0,
          total_variants: 0,
          available_variants: 0,
        }
      };
    }

    const statsData = stats[0];
    return { 
      success: true, 
      data: {
        total_products: Number(statsData.total_products) || 0,
        products_with_variants: Number(statsData.products_with_variants) || 0,
        total_variants: Number(statsData.total_variants) || 0,
        available_variants: Number(statsData.available_variants) || 0,
      }
    };
  } catch (error) {
    console.error("Unexpected error in getBusinessVariantStats:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}

// Check if a variant combination is unique within a product
export async function checkVariantCombinationUnique(
  productId: string,
  variantValues: Record<string, string>,
  excludeVariantId?: string
): Promise<{ success: boolean; error?: string; isUnique?: boolean }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user)
    return { success: false, error: "User not authenticated." };

  try {
    // Verify that the product belongs to the authenticated user
    const { data: product, error: ownershipError } = await supabase
      .from("products_services")
      .select("business_id")
      .eq("id", productId)
      .eq("business_id", user.id)
      .single();

    if (ownershipError || !product) {
      return { success: false, error: "Product not found or access denied." };
    }

    // Use the database function to check uniqueness
    const { data: result, error: functionError } = await supabase
      .rpc("is_variant_combination_unique", { 
        product_uuid: productId,
        variant_vals: variantValues,
        exclude_variant_id: excludeVariantId || undefined
      });

    if (functionError) {
      console.error("Error calling is_variant_combination_unique:", functionError);
      return { success: false, error: "Failed to validate variant uniqueness." };
    }

    return { success: true, isUnique: result };
  } catch (error) {
    console.error("Unexpected error in checkVariantCombinationUnique:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}
