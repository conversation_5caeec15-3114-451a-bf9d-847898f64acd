import { createClient } from "@/utils/supabase/server";
import { notFound } from "next/navigation";
import { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { Blog } from "@/lib/types/blog";
import {
  generateBlogMetadata,
  generateBlogJsonLd,
  generateBlogBreadcrumbJsonLd,
} from "@/lib/utils/seo";
import { calculateReadingTime } from "@/lib/utils/markdown";
import BlogPostClient from "./components/BlogPostClient";
import BlogPostSkeleton from "@/components/blog/BlogPostSkeleton";

interface BlogPostPageProps {
  params: Promise<{ blogSlug: string }>;
}

async function fetchBlogBySlug(slug: string): Promise<Blog | null> {
  const supabase = await createClient();

  try {
    const { data: blog, error } = await supabase
      .from("blogs")
      .select("*")
      .eq("slug", slug)
      .eq("status", "published")
      .single();

    if (error || !blog) {
      return null;
    }

    // Calculate reading time if not already set
    const readingTime =
      blog.reading_time_minutes || calculateReadingTime(blog.content);

    return {
      ...blog,
      reading_time_minutes: readingTime,
      categories: blog.categories || [],
      tags: blog.tags || [],
    };
  } catch (error) {
    console.error("Error fetching blog:", error);
    return null;
  }
}

async function fetchRelatedBlogs(
  currentBlogId: string,
  categories: string[],
  limit: number = 3
): Promise<Blog[]> {
  const supabase = await createClient();

  try {
    let query = supabase
      .from("blogs")
      .select("*")
      .eq("status", "published")
      .neq("id", currentBlogId)
      .limit(limit);

    // If categories exist, try to find related posts
    if (categories.length > 0) {
      query = query.overlaps("categories", categories);
    }

    query = query.order("published_at", { ascending: false });

    const { data: blogs, error } = await query;

    if (error) {
      console.error("Error fetching related blogs:", error);
      return [];
    }

    return (blogs || []).map((blog: any) => ({
      ...blog,
      reading_time_minutes:
        blog.reading_time_minutes || calculateReadingTime(blog.content),
      categories: blog.categories || [],
      tags: blog.tags || [],
    }));
  } catch (error) {
    console.error("Error in fetchRelatedBlogs:", error);
    return [];
  }
}

export async function generateMetadata({
  params,
}: BlogPostPageProps): Promise<Metadata> {
  const { blogSlug } = await params;
  const blog = await fetchBlogBySlug(blogSlug);

  if (!blog) {
    return {
      title: "Blog Post Not Found",
      description: "The requested blog post could not be found.",
    };
  }

  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "";
  const metadata = generateBlogMetadata(blog, baseUrl);

  return {
    title: metadata.title,
    description: metadata.description,
    keywords: [...(blog.tags || []), ...(blog.categories || [])].join(", "),
    authors: [{ name: metadata.author }],
    openGraph: {
      title: metadata.title,
      description: metadata.description,
      type: "article",
      url: metadata.url,
      images: metadata.image
        ? [{ url: metadata.image, alt: metadata.title }]
        : [],
      publishedTime: metadata.publishedTime || undefined,
      authors: [metadata.author],
      tags: metadata.tags,
    },
    twitter: {
      card: "summary_large_image",
      title: metadata.title,
      description: metadata.description,
      images: metadata.image ? [metadata.image] : [],
    },
    alternates: {
      canonical: metadata.url,
    },
  };
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { blogSlug } = await params;
  const blog = await fetchBlogBySlug(blogSlug);

  if (!blog) {
    notFound();
  }

  const relatedBlogs = await fetchRelatedBlogs(
    blog.id,
    blog.categories || [],
    3
  );
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "";

  // Generate structured data
  const blogJsonLd = generateBlogJsonLd(blog, baseUrl);
  const breadcrumbJsonLd = generateBlogBreadcrumbJsonLd(blog, baseUrl);

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(blogJsonLd) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbJsonLd) }}
      />

      <Suspense fallback={<BlogPostSkeleton />}>
        <BlogPostClient blog={blog} relatedBlogs={relatedBlogs} />
      </Suspense>
    </>
  );
}
