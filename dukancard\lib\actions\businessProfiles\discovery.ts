"use server";

import { createClient } from "@/utils/supabase/server";
import { BusinessProfilePublicData, BusinessSortBy } from "./types";
import { applySorting } from "./utils";

/**
 * Securely fetch business profiles for discover page using the service role key
 */
export async function getSecureBusinessProfilesForDiscover(
  pincodes: string | string[],
  locality?: string | null,
  page: number = 1,
  limit: number = 10,
  sortBy: BusinessSortBy = "created_desc"
): Promise<{
  data?: BusinessProfilePublicData[];
  count?: number;
  error?: string;
}> {
  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {
    return { error: "At least one pincode is required." };
  }

  // Convert single pincode to array for consistent handling
  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];

  try {
    // Use the admin client with service role key to bypass RLS
    const supabase = await createClient();
    const offset = (page - 1) * limit;

    // Get total count of online businesses
    const countQuery = supabase
      .from("business_profiles")
      .select("id", { count: "exact" })
      .in("pincode", pincodeArray)
      .eq("status", "online");

    // Add locality filter if provided
    if (locality) {
      countQuery.eq("locality", locality);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Count Error:", countError);
      return { error: "Database error counting profiles." };
    }

    // If there are no profiles, return empty array
    if (!count || count === 0) {
      return { data: [], count: 0 };
    }

    // Define fields to select
    const businessFields = `
      id, business_name, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
      delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
      business_category, trial_end_date, created_at, updated_at, contact_email, established_year,
      custom_branding, custom_ads
    `;

    // Build the query
    let businessQuery = supabase
      .from("business_profiles")
      .select(businessFields)
      .in("pincode", pincodeArray)
      .eq("status", "online")
      .range(offset, offset + limit - 1);

    // Add locality filter if provided
    if (locality) {
      businessQuery.eq("locality", locality);
    }

    // Apply sorting
    businessQuery = applySorting(businessQuery, sortBy);

    // Execute the query
    const { data, error } = await businessQuery;

    if (error) {
      console.error("Query Error:", error);
      return { error: "Database error fetching profiles." };
    }

    // Filter out sensitive data before returning (no subscription data needed)
    interface BusinessProfileRow {
      id: string;
      business_name: string | null;
      logo_url: string | null;
      member_name: string | null;
      title: string | null;
      address_line: string | null;
      city: string | null;
      state: string | null;
      pincode: string | null;
      locality: string | null;
      phone: string | null;
      instagram_url: string | null;
      facebook_url: string | null;
      whatsapp_number: string | null;
      about_bio: string | null;
      status: string | null;
      business_slug: string | null;
      theme_color: string | null;
      delivery_info: string | null;
      total_likes: number | null;
      total_subscriptions: number | null;
      average_rating: number | null;
      business_hours: any | null; // Assuming business_hours can be any type for now
      business_category: string | null;
      trial_end_date: string | null;
      created_at: string | null;
      updated_at: string | null;
      contact_email: string | null;
      established_year: number | null;
      custom_branding: any;
      custom_ads: boolean | null;
    }

    const safeData: BusinessProfilePublicData[] = data.map((profile: BusinessProfileRow) => {
      return {
        ...profile,
        // Add missing fields with default values
        has_active_subscription: false, // Default to false
        total_visits: 0,
        today_visits: 0,
        yesterday_visits: 0,
        visits_7_days: 0,
        visits_30_days: 0,
        city_slug: null,
        state_slug: null,
        locality_slug: null,
        gallery: null,
        latitude: null,
        longitude: null,
        subscription_status: null, // Not available in business_profiles table
        plan_id: null, // Not available in business_profiles table
      } as BusinessProfilePublicData;
    });

    return { data: safeData, count: count || 0 };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfilesForDiscover:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetch business profile IDs for discover page products using the service role key
 */
export async function getSecureBusinessProfileIdsForDiscover(
  pincodes: string | string[],
  locality?: string | null,
  sortBy: BusinessSortBy = "created_desc"
): Promise<{
  data?: string[];
  error?: string;
}> {
  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {
    return { error: "At least one pincode is required." };
  }

  // Convert single pincode to array for consistent handling
  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];

  try {
    // Use the admin client with service role key to bypass RLS
    const supabase = await createClient();

    // Build query for online businesses
    let validBusinessQuery = supabase
      .from("business_profiles")
      .select("id")
      .in("pincode", pincodeArray)
      .eq("status", "online");

    // Add locality filter if provided
    if (locality) {
      validBusinessQuery.eq("locality", locality);
    }

    // Apply sorting
    validBusinessQuery = applySorting(validBusinessQuery, sortBy);

    // Execute the query
    const { data, error } = await validBusinessQuery;

    if (error) {
      console.error("Query Error:", error);
      return { error: "Database error fetching profile IDs." };
    }

    // We only need the IDs for this function, so we don't need to fetch subscription data
    // Just return the IDs
    const safeData = data.map((profile: { id: string }) => profile.id);

    return { data: safeData };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfileIdsForDiscover:", e);
    return { error: "An unexpected error occurred." };
  }
}
