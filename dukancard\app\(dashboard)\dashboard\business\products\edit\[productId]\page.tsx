import { Metadata } from "next";
import { createClient } from "@/utils/supabase/server";
import { redirect, notFound } from "next/navigation";
import EditProductClient from "./EditProductClient";
import { getPlanLimit } from "@/lib/PricingPlans";
import { ProductVariant } from "@/types/variants";

export const metadata: Metadata = {
  title: "Edit Product",
  robots: "noindex, nofollow",
};

export default async function EditProductPage({
  params,
}: {
  params: Promise<{ productId: string }>;
}) {
  const { productId } = await params;
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/login?message=Authentication required");
  }

  // Fetch the product to edit
  const { data: product, error: productError } = await supabase
    .from("products_services")
    .select(`
      id,
      business_id,
      product_type,
      name,
      description,
      base_price,
      discounted_price,
      is_available,
      image_url,
      images,
      featured_image_index,
      created_at,
      updated_at,
      slug
    `)
    .eq("id", productId)
    .eq("business_id", user.id)
    .single();

  // If the product doesn't exist or doesn't belong to the user, return 404
  if (productError || !product) {
    console.error("Error fetching product:", productError?.message);
    notFound();
  }

  // Get the user's current plan from payment_subscriptions
  const { data: subscriptionData, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("plan_id")
    .eq("business_profile_id", user.id)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
  }

  // Default to free plan if no subscription found
  const planId = subscriptionData?.plan_id || "free";
  const planLimit = getPlanLimit(planId);

  // Count how many products are currently available (excluding this one if it's not available)
  const { count: availableCount, error: countError } = await supabase
    .from("products_services")
    .select("id", { count: "exact", head: true })
    .eq("business_id", user.id)
    .eq("is_available", true)
    .not("id", "eq", productId); // Exclude this product if it's being edited

  if (countError) {
    console.error("Error counting available products:", countError);
  }

  // If this product is already available, we need to include it in the count
  const currentAvailableCount = (availableCount || 0) + (product.is_available ? 1 : 0);

  // Fetch existing variants for this product
  const { data: variantsData, error: variantsError } = await supabase
    .from("product_variants")
    .select(`
      id,
      product_id,
      variant_name,
      variant_values,
      base_price,
      discounted_price,
      is_available,
      images,
      featured_image_index,
      created_at,
      updated_at
    `)
    .eq("product_id", productId)
    .order("created_at", { ascending: true });

  if (variantsError) {
    console.error("Error fetching product variants:", variantsError);
  }

  // Transform variants to ensure variant_values is parsed correctly
  const variants: ProductVariant[] = (variantsData || []).map((variant: any) => ({
    ...variant,
    variant_values: typeof variant.variant_values === 'string'
      ? JSON.parse(variant.variant_values)
      : variant.variant_values,
  }));

  // Transform product to match expected type
  const transformedProduct: ProductServiceData = {
    ...product,
    base_price: product.base_price || 0,
    product_type: (product.product_type as "physical" | "service") || "physical",
  };

  return (
    <EditProductClient
      product={transformedProduct}
      variants={variants}
      planLimit={planLimit}
      currentAvailableCount={currentAvailableCount}
    />
  );
}
