"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Edit, Trash2, Package, ChevronRight, MoreHorizontal } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import Link from "next/link";
import { viewTransitionVariants } from "../../types";
import { useProducts } from "../../context/ProductsContext";
import ProductEmptyState from "./ProductEmptyState";
import VariantTable from "../VariantTable";
import { getProductWithVariants } from "../../actions/getProductWithVariants";
import { ProductVariant, RpcProductVariant } from "@/types/variants";
import { toast } from "sonner";

export default function ProductTable() {
  const {
    products,
    isLoading,
    isPending,
    deletingProductId,
    setDeletingProductId
  } = useProducts();

  // State for expanded products and their variants
  const [expandedProducts, setExpandedProducts] = useState<Set<string>>(new Set());
  const [productVariants, setProductVariants] = useState<Record<string, RpcProductVariant[]>>({});
  const [loadingVariants, setLoadingVariants] = useState<Set<string>>(new Set());

  // Toggle product expansion and load variants if needed
  const toggleProductExpansion = async (productId: string) => {
    const newExpanded = new Set(expandedProducts);

    if (newExpanded.has(productId)) {
      // Collapse
      newExpanded.delete(productId);
    } else {
      // Expand and load variants if not already loaded
      newExpanded.add(productId);

      if (!productVariants[productId]) {
        setLoadingVariants(prev => new Set(prev).add(productId));

        try {
          const result = await getProductWithVariants(productId);
          if (result.success && result.data) {
            setProductVariants(prev => ({
              ...prev,
              [productId]: result.data?.variants || []
            }));
          } else {
            toast.error("Failed to load product variants");
            newExpanded.delete(productId); // Don't expand if loading failed
          }
        } catch (error) {
          console.error("Error loading variants:", error);
          toast.error("Failed to load product variants");
          newExpanded.delete(productId); // Don't expand if loading failed
        } finally {
          setLoadingVariants(prev => {
            const newSet = new Set(prev);
            newSet.delete(productId);
            return newSet;
          });
        }
      }
    }

    setExpandedProducts(newExpanded);
  };

  return (
    <motion.div
      key="table-view"
      variants={viewTransitionVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="space-y-6"
    >
      <div className="rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm overflow-hidden shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-neutral-200/60 dark:border-neutral-800/60 bg-gradient-to-r from-neutral-50/80 to-neutral-100/40 dark:from-neutral-800/50 dark:to-neutral-900/30 hover:from-neutral-100/80 hover:to-neutral-150/40 dark:hover:from-neutral-700/50 dark:hover:to-neutral-800/30 transition-all duration-200">
              <TableHead className="w-12 text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4"></TableHead>
              <TableHead className="w-20 text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4">Image</TableHead>
              <TableHead className="text-left text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4 px-6">
                Product Details
              </TableHead>
              <TableHead className="text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4">
                Category
              </TableHead>
              <TableHead className="text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4">
                Variants
              </TableHead>
              <TableHead className="text-right text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4">
                Pricing
              </TableHead>
              <TableHead className="text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4">
                Status
              </TableHead>
              <TableHead className="text-center text-sm font-semibold text-neutral-700 dark:text-neutral-300 py-4 w-20">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!isLoading && products.length === 0 && (
              <TableRow>
                <TableCell
                  colSpan={8}
                  className="h-32 text-center"
                >
                  <ProductEmptyState view="table" />
                </TableCell>
              </TableRow>
            )}
            {products.map((product, index) => {
              const isExpanded = expandedProducts.has(product.id);
              const isLoadingVariants = loadingVariants.has(product.id);
              const hasVariants = product.variant_count > 0;

              return (
                <React.Fragment key={product.id}>
                  <motion.tr
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className={`
                      group border-b border-neutral-100/60 dark:border-neutral-800/60
                      hover:bg-gradient-to-r hover:from-neutral-50/80 hover:to-neutral-100/40
                      dark:hover:from-neutral-800/50 dark:hover:to-neutral-900/30
                      ${isPending && deletingProductId === product.id ? 'opacity-50 pointer-events-none' : ''}
                      ${isExpanded ? 'bg-gradient-to-r from-primary/5 to-primary/2 dark:from-primary/10 dark:to-primary/5' : ''}
                      transition-all duration-300
                    `}
                  >
                    {/* Expand/Collapse Button */}
                    <TableCell className="py-4 px-4">
                      {hasVariants ? (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleProductExpansion(product.id)}
                          disabled={isLoadingVariants}
                          className="h-8 w-8 p-0 rounded-lg hover:bg-primary/10 transition-colors duration-200"
                        >
                          {isLoadingVariants ? (
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            >
                              <Package className="h-4 w-4 text-primary" />
                            </motion.div>
                          ) : (
                            <motion.div
                              animate={{ rotate: isExpanded ? 90 : 0 }}
                              transition={{ duration: 0.3, ease: "easeInOut" }}
                            >
                              <ChevronRight className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
                            </motion.div>
                          )}
                        </Button>
                      ) : (
                        <div className="h-8 w-8" />
                      )}
                    </TableCell>

                    {/* Product Image */}
                    <TableCell className="py-4 px-4">
                      <div className="flex justify-center">
                        {product.image_url ? (
                          <div className="relative w-12 h-12 rounded-xl overflow-hidden border border-neutral-200/60 dark:border-neutral-700/60 shadow-sm group-hover:shadow-md transition-all duration-300">
                            <Image
                              src={product.image_url}
                              alt={product.name ?? "Product image"}
                              className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-110"
                              width={48}
                              height={48}
                            />
                          </div>
                        ) : (
                          <div className="w-12 h-12 bg-gradient-to-br from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-900 rounded-xl flex items-center justify-center text-neutral-400 dark:text-neutral-500 border border-neutral-200/60 dark:border-neutral-700/60">
                            <Package className="w-6 h-6" />
                          </div>
                        )}
                      </div>
                    </TableCell>
                    {/* Product Details */}
                    <TableCell className="py-4 px-6">
                      <div className="space-y-1">
                        <div className="font-semibold text-sm text-neutral-900 dark:text-neutral-100 max-w-xs truncate">
                          {product.name}
                        </div>
                        {product.description && (
                          <div className="text-xs text-neutral-500 dark:text-neutral-400 max-w-xs truncate">
                            {product.description}
                          </div>
                        )}
                      </div>
                    </TableCell>

                    {/* Category */}
                    <TableCell className="py-4 px-4 text-center">
                      <Badge
                        variant="secondary"
                        className="capitalize text-xs font-medium bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 border-0 px-3 py-1 rounded-full"
                      >
                        {product.product_type}
                      </Badge>
                    </TableCell>

                    {/* Variants */}
                    <TableCell className="py-4 px-4 text-center">
                      {product.variant_count > 0 ? (
                        <div className="space-y-1">
                          <div className="font-semibold text-sm text-neutral-900 dark:text-neutral-100">
                            {product.variant_count}
                          </div>
                          <div className="text-xs text-neutral-500 dark:text-neutral-400">
                            {product.available_variant_count} active
                          </div>
                        </div>
                      ) : (
                        <div className="text-xs text-neutral-400 dark:text-neutral-500 font-medium">
                          No variants
                        </div>
                      )}
                    </TableCell>

                    {/* Pricing */}
                    <TableCell className="py-4 px-4 text-right">
                      <div className="space-y-1">
                        <div className="font-semibold text-sm text-neutral-900 dark:text-neutral-100">
                          {product.base_price?.toLocaleString("en-IN", {
                            style: "currency",
                            currency: "INR",
                          }) ?? "—"}
                        </div>
                        {product.discounted_price && (
                          <div className="text-xs text-emerald-600 dark:text-emerald-400 font-medium">
                            {product.discounted_price.toLocaleString("en-IN", {
                              style: "currency",
                              currency: "INR",
                            })}
                          </div>
                        )}
                      </div>
                    </TableCell>

                    {/* Status */}
                    <TableCell className="py-4 px-4 text-center">
                      <Badge
                        variant={product.is_available ? "default" : "secondary"}
                        className={`
                          text-xs font-medium px-3 py-1 rounded-full border-0
                          ${product.is_available
                            ? 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400'
                            : 'bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-400'
                          }
                        `}
                      >
                        {product.is_available ? 'Available' : 'Unavailable'}
                      </Badge>
                    </TableCell>
                    {/* Actions */}
                    <TableCell className="py-4 px-4 text-center">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            disabled={isPending}
                            className="h-8 w-8 p-0 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors duration-200"
                          >
                            <MoreHorizontal className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="w-48 rounded-xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm shadow-lg"
                        >
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/business/products/edit/${product.id}`}
                              className="flex items-center gap-2 px-3 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-colors duration-200 cursor-pointer"
                            >
                              <Edit className="h-4 w-4" />
                              Edit Product
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <button
                              onClick={() => setDeletingProductId(product.id!)}
                              disabled={isPending || deletingProductId === product.id}
                              className="flex items-center gap-2 px-3 py-2 text-sm text-rose-600 dark:text-rose-400 hover:bg-rose-50 dark:hover:bg-rose-900/20 rounded-lg transition-colors duration-200 cursor-pointer w-full text-left disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <Trash2 className="h-4 w-4" />
                              Delete Product
                            </button>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </motion.tr>

                  {/* Expanded Variant Section */}
                  <AnimatePresence>
                    {isExpanded && hasVariants && productVariants[product.id] && (
                      <motion.tr
                        key={`expanded-${product.id}`}
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="bg-gradient-to-r from-neutral-50/80 to-neutral-100/40 dark:from-neutral-800/50 dark:to-neutral-900/30 border-b border-neutral-200/60 dark:border-neutral-800/60"
                      >
                        <TableCell colSpan={8} className="p-0">
                          <div className="p-6 border-l-4 border-primary/20">
                            <div className="mb-4">
                              <h4 className="text-sm font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                                Product Variants
                              </h4>
                              <p className="text-xs text-neutral-500 dark:text-neutral-400">
                                Manage different variations of this product
                              </p>
                            </div>
                            <VariantTable
                              productId={product.id}
                              variants={productVariants[product.id]}
                              className="border-0 bg-transparent rounded-xl"
                              onAddVariant={() => {
                                window.location.href = `/dashboard/business/products/edit/${product.id}?tab=variants`;
                              }}
                              onEditVariant={(variant) => {
                                window.location.href = `/dashboard/business/products/edit/${product.id}?tab=variants&variant=${variant.id}`;
                              }}
                              onDeleteVariant={(variantId) => {
                                console.log("Delete variant:", variantId);
                              }}
                              onToggleVariantAvailability={(variantId, isAvailable) => {
                                console.log("Toggle variant availability:", variantId, isAvailable);
                              }}
                            />
                          </div>
                        </TableCell>
                      </motion.tr>
                    )}
                  </AnimatePresence>
                </React.Fragment>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </motion.div>
  );
}
