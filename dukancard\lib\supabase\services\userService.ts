import { createClient } from "@/utils/supabase/server";
import { TABLES, COLUMNS } from "../constants";
import { Tables, TablesInsert, TablesUpdate } from "../../../types/supabase";

type BusinessProfileAnalyticsRow = Pick<Tables<'business_profiles'>, 'total_visits' | 'today_visits' | 'yesterday_visits' | 'visits_7_days' | 'visits_30_days'>;

/**
 * Fetches a user's customer profile.
 * @param userId The ID of the user to fetch the profile for.
 * @returns An object containing the user's profile data or an error.
 */
export async function getUserProfile(userId: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .select("*")
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error("Error fetching user profile:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching user profile:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Creates a new customer profile.
 * @param profile The profile data to insert.
 * @returns An object containing the newly created profile data or an error.
 */
export async function createUserProfile(profile: TablesInsert<'customer_profiles'>) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .insert([profile])
      .select()
      .single();

    if (error) {
      console.error("Error creating user profile:", error.message);
      return { data: null, error: error.message };
    }
    return { data: data?.[0] || null, error: null };
  } catch (err) {
    console.error("Unexpected error creating user profile:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Checks if a customer profile exists for a given user ID.
 * @param userId The ID of the user to check.
 * @returns An object containing a boolean indicating if the profile exists and an error if one occurred.
 */
export async function checkIfCustomerProfileExists(userId: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, userId)
      .maybeSingle();

    if (error) {
      console.error("Error checking existing profile:", error.message);
      return { exists: false, error: "Database error checking profile." };
    }

    return { exists: !!data, error: null };
  } catch (err) {
    console.error("Unexpected error checking profile:", err);
    return { exists: false, error: "An unexpected error occurred." };
  }
}

/**
 * Updates a user's profile.
 * @param userId The ID of the user to update.
 * @param updates The profile data to update.
 * @returns An object containing the updated profile data or an error.
 */
export async function updateUserProfile(userId: string, updates: TablesUpdate<'customer_profiles'>) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .update(updates)
      .eq(COLUMNS.ID, userId)
      .select()
      .single();

    if (error) {
      console.error("Error updating user profile:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error updating user profile:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Checks if a business profile exists for a given user ID.
 * @param userId The ID of the user to check.
 * @returns An object containing a boolean indicating if the profile exists and an error if one occurred.
 */
export async function checkIfBusinessProfileExists(userId: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, userId)
      .maybeSingle();

    if (error) {
      console.error("Error checking existing business profile:", error.message);
      return { exists: false, error: "Database error checking business profile." };
    }

    return { exists: !!data, error: null };
  } catch (err) {
    console.error("Unexpected error checking business profile:", err);
    return { exists: false, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the currently authenticated user.
 * @returns An object containing the user data or an error.
 */
export async function getAuthenticatedUser() {
  const supabase = await createClient();
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error("Error fetching authenticated user:", error.message);
      return { user: null, error: "User not found or authentication error." };
    }
    return { user, error: null };
  } catch (err) {
    console.error("Unexpected error fetching authenticated user:", err);
    return { user: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches analytics data for a business profile.
 * @param businessProfileId The ID of the business profile.
 * @returns An object containing the business profile analytics data or an error.
 */
export async function getBusinessProfileAnalyticsData(businessProfileId: string): Promise<{ data: BusinessProfileAnalyticsRow | null; error: string | null }> {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select("total_visits, today_visits, yesterday_visits, visits_7_days, visits_30_days")
      .eq(COLUMNS.ID, businessProfileId)
      .single();

    if (error) {
      console.error("Error fetching business profile analytics data:", error.message);
      return { data: null, error: error.message };
    }
    return { data: data as BusinessProfileAnalyticsRow, error: null };
  } catch (err) {
    console.error("Unexpected error fetching business profile analytics data:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_daily_unique_visit_trend' RPC function.
 * @param businessId The ID of the business.
 * @param startDate The start date for the trend (YYYY-MM-DD).
 * @param endDate The end date for the trend (YYYY-MM-DD).
 * @returns An object containing the daily unique visit trend data or an error.
 */
export async function getDailyUniqueVisitTrend(businessId: string, startDate: string, endDate: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_daily_unique_visit_trend", {
      business_id: businessId,
      start_date: startDate,
      end_date: endDate,
    });
    if (error) {
      console.error("Error fetching daily unique visit trend:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching daily unique visit trend:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_hourly_unique_visit_trend' RPC function.
 * @param businessId The ID of the business.
 * @param targetDate The target date for the hourly trend (YYYY-MM-DD).
 * @returns An object containing the hourly unique visit trend data or an error.
 */
export async function getHourlyUniqueVisitTrend(businessId: string, targetDate: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_hourly_unique_visit_trend", {
      business_id: businessId,
      target_date: targetDate,
    });
    if (error) {
      console.error("Error fetching hourly unique visit trend:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching hourly unique visit trend:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_monthly_unique_visits' RPC function.
 * @param businessId The ID of the business.
 * @param targetYear The target year.
 * @param targetMonth The target month.
 * @returns An object containing the monthly unique visits count or an error.
 */
export async function getMonthlyUniqueVisits(businessId: string, targetYear: number, targetMonth: number) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_monthly_unique_visits", {
      business_id: businessId,
      target_year: targetYear,
      target_month: targetMonth,
    });
    if (error) {
      console.error("Error fetching monthly unique visits:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching monthly unique visits:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_monthly_unique_visit_trend' RPC function.
 * @param businessId The ID of the business.
 * @param startYear The start year for the trend.
 * @param startMonth The start month for the trend.
 * @param endYear The end year for the trend.
 * @param endMonth The end month for the trend.
 * @returns An object containing the monthly unique visit trend data or an error.
 */
export async function getMonthlyUniqueVisitTrend(businessId: string, startYear: number, startMonth: number, endYear: number, endMonth: number) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_monthly_unique_visit_trend", {
      business_id: businessId,
      start_year: startYear,
      start_month: startMonth,
      end_year: endYear,
      end_month: endMonth,
    });
    if (error) {
      console.error("Error fetching monthly unique visit trend:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching monthly unique visit trend:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_available_years_for_monthly_metrics' RPC function.
 * @param businessId The ID of the business.
 * @returns An object containing the available years for monthly metrics or an error.
 */
export async function getAvailableYearsForMonthlyMetrics(businessId: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_available_years_for_monthly_metrics", {
      business_id: businessId,
    });
    if (error) {
      console.error("Error fetching available years for monthly metrics:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching available years for monthly metrics:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_total_unique_visits' RPC function.
 * @param businessId The ID of the business.
 * @returns An object containing the total unique visits count or an error.
 */
export async function getTotalUniqueVisits(businessId: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.rpc("get_total_unique_visits", {
      business_id: businessId,
    });
    if (error) {
      console.error("Error fetching total unique visits:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching total unique visits:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}