import React from 'react';
import { render, screen, act } from '@testing-library/react';
import EditProductClient from '@/app/(dashboard)/dashboard/business/products/edit/[productId]/EditProductClient';
import { ProductServiceData } from '@/app/(dashboard)/dashboard/business/products/actions';
import * as productActions from '@/app/(dashboard)/dashboard/business/products/actions';
import { toast } from 'sonner';
import { ProductVariant } from '@/types/variants';

// Mock dependencies
const mockRouterPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockRouterPush,
  }),
}));

jest.mock('sonner');

let mockOnSubmit: (values: any, imageFiles?: any[], featuredImageIndex?: number, removedImageIndices?: number[]) => void;

jest.mock(
  '@/app/(dashboard)/dashboard/business/products/components/StandaloneProductForm',
  () => {
    return function DummyStandaloneProductForm(props: any) {
      mockOnSubmit = props.onSubmit;
      return (
        <form data-testid="standalone-product-form" onSubmit={(e) => e.preventDefault()}>
          <button type="button" onClick={() => props.onSubmit({ name: 'Test Submit' })}>
            Submit
          </button>
        </form>
      );
    };
  }
);

jest.mock('@/app/(dashboard)/dashboard/business/products/actions', () => ({
    ...jest.requireActual('@/app/(dashboard)/dashboard/business/products/actions'),
    updateProductService: jest.fn(),
    addProductVariant: jest.fn(),
    updateProductVariant: jest.fn(),
    deleteProductVariant: jest.fn(),
}));

const mockProduct: ProductServiceData = {
  id: 'prod_1',
  business_id: 'bus_1',
  product_type: 'service',
  name: 'Test Product',
  description: 'Test Description',
  base_price: 100,
  discounted_price: 80,
  is_available: true,
  image_url: 'http://example.com/image.png',
  images: ['http://example.com/image.png'],
  featured_image_index: 0,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  slug: 'test-product',
};

const mockInitialVariants: ProductVariant[] = [
    { id: 'var_to_update', product_id: 'prod_1', variant_name: 'Old Name', variant_values: {}, base_price: 1, discounted_price: null, is_available: true, images: [], featured_image_index: 0, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    { id: 'var_to_delete', product_id: 'prod_1', variant_name: 'Delete Me', variant_values: {}, base_price: 1, discounted_price: null, is_available: true, images: [], featured_image_index: 0, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
];


describe('EditProductClient', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should render the edit product form with initial data', () => {
        render(
            <EditProductClient
                product={mockProduct}
                variants={mockInitialVariants}
                planLimit={10}
                currentAvailableCount={5}
            />
        );

        expect(screen.getByText('Edit Product')).toBeInTheDocument();
        expect(screen.getByText(/Update details for "Test Product"/)).toBeInTheDocument();
        expect(screen.getByTestId('standalone-product-form')).toBeInTheDocument();
    });

    it('should call updateProductService on form submission and redirect on success', async () => {
        const mockUpdateProductService = productActions.updateProductService as jest.Mock;
        mockUpdateProductService.mockResolvedValue({ success: true, data: { id: 'prod_1' } });

        render(<EditProductClient product={mockProduct} />);

        const updatedData = { name: 'Updated Product Name' };

        await act(async () => {
            mockOnSubmit(updatedData);
        });

        expect(mockUpdateProductService).toHaveBeenCalledWith('prod_1', expect.any(FormData));
        expect(toast.success).toHaveBeenCalledWith('Product updated successfully!');
        expect(mockRouterPush).toHaveBeenCalledWith('/dashboard/business/products');
    });

    it('should handle variant updates, creations, and deletions on submit', async () => {
        const mockUpdateProductService = productActions.updateProductService as jest.Mock;
        const mockUpdateProductVariant = productActions.updateProductVariant as jest.Mock;
        const mockAddProductVariant = productActions.addProductVariant as jest.Mock;
        const mockDeleteProductVariant = productActions.deleteProductVariant as jest.Mock;

        mockUpdateProductService.mockResolvedValue({ success: true, data: { id: 'prod_1' } });
        mockUpdateProductVariant.mockResolvedValue({ success: true });
        mockAddProductVariant.mockResolvedValue({ success: true });
        mockDeleteProductVariant.mockResolvedValue({ success: true });

        render(<EditProductClient product={mockProduct} variants={mockInitialVariants} />);

        const submittedVariants = [
            // Updated variant
            { id: 'var_to_update', variant_name: 'New Name', variant_values: { tier: 'Premium' } },
            // New variant
            { id: 'temp-123', variant_name: 'Brand New Variant', variant_values: { size: 'Large' } },
        ];
        
        const updatedDataWithVariants = {
            name: 'Product with variant changes',
            variants: submittedVariants,
        };

        await act(async () => {
            mockOnSubmit(updatedDataWithVariants);
        });

        expect(mockUpdateProductService).toHaveBeenCalled();
        expect(mockDeleteProductVariant).toHaveBeenCalledWith('var_to_delete');
        expect(mockUpdateProductVariant).toHaveBeenCalledWith('var_to_update', expect.any(FormData));
        expect(mockAddProductVariant).toHaveBeenCalledWith(expect.any(FormData));
        expect(toast.success).toHaveBeenCalledWith('Product updated! Processing variants...');
    });

    it('should show an error toast if updateProductService fails', async () => {
        const mockUpdateProductService = productActions.updateProductService as jest.Mock;
        mockUpdateProductService.mockResolvedValue({ success: false, error: 'Update failed' });

        render(<EditProductClient product={mockProduct} />);

        await act(async () => {
            mockOnSubmit({ name: 'Failed Update' });
        });

        expect(toast.error).toHaveBeenCalledWith('Failed to update product', {
            description: 'Update failed',
        });
    });
});